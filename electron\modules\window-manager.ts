import { BrowserWindow } from 'electron';
import path from 'node:path';
import { createLogger } from '../../src/libs/logger';

const logger = createLogger('WindowManager');

class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private floatingWindow: BrowserWindow | null = null;
  private preloadPath: string;
  private viteDevServerUrl: string | undefined;
  // TODO: 换为实际生产地址
  private webProdUrl = 'http://172.16.0.117:8950/';
  private isUserLoggedIn: boolean = false;

  constructor(preloadPath: string) {
    this.preloadPath = preloadPath;
    this.viteDevServerUrl = process.env['VITE_DEV_SERVER_URL'];
  }

  createMainWindow(): BrowserWindow {
    this.mainWindow = new BrowserWindow({
      show: false,
      webPreferences: {
        preload: this.preloadPath,
        webSecurity: false,
        nodeIntegration: true,
      },
      frame: false,
      width: 1000,
      height: 562.5,
      minWidth: 1000,
      minHeight: 562.5,
      backgroundColor: '#eee',
    });

    if (this.viteDevServerUrl) {
      this.mainWindow.loadURL(this.viteDevServerUrl);
    } else {
      this.mainWindow.loadURL(this.webProdUrl);
    }

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    this.mainWindow.on('maximize', () => {
      this.mainWindow?.webContents.send('window-state-changed', this.mainWindow.isMaximized());
    });

    this.mainWindow.on('unmaximize', () => {
      this.mainWindow?.webContents.send('window-state-changed', this.mainWindow.isMaximized());
    });

    logger.info('主窗口创建完成');
    return this.mainWindow;
  }

  createFloatingWindow(): BrowserWindow {
    this.floatingWindow = new BrowserWindow({
      width: 1000,
      height: 40,
      frame: false,
      alwaysOnTop: true,
      skipTaskbar: true,
      webPreferences: {
        preload: this.preloadPath,
        webSecurity: false,
        nodeIntegration: true,
      },
      backgroundColor: 'transparent',
      show: false,
    });

    // 加载悬浮窗口页面
    if (this.viteDevServerUrl) {
      this.floatingWindow.loadURL(`${this.viteDevServerUrl}#/floating`);
    } else {
      this.floatingWindow.loadURL(`${this.webProdUrl}#/floating`);
    }

    this.floatingWindow.once('ready-to-show', () => {
      this.floatingWindow?.show();
    });

    this.floatingWindow.on('closed', () => {
      this.floatingWindow = null;
    });

    logger.info('悬浮窗口创建完成');
    return this.floatingWindow;
  }

  getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  getFloatingWindow(): BrowserWindow | null {
    return this.floatingWindow;
  }

  showFloatingWindow(): void {
    // 只有在用户已登录时才显示悬浮窗口
    if (!this.isUserLoggedIn) {
      logger.warn('用户未登录，无法显示悬浮窗口');
      return;
    }

    if (!this.floatingWindow) {
      this.createFloatingWindow();
    } else {
      this.floatingWindow.show();
    }
  }

  closeFloatingWindow(): void {
    if (this.floatingWindow) {
      this.floatingWindow.close();
      this.floatingWindow = null;
    }
  }

  minimizeMainWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.minimize();
    }
  }

  closeMainWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.webContents.session.clearStorageData({
        storages: ['localstorage'],
      });
      this.mainWindow.close();
    }
    // 主窗口关闭时，同时关闭悬浮窗口
    this.closeFloatingWindow();
  }

  maximizeMainWindow(maximize?: boolean): void {
    if (!this.mainWindow) return;

    if (maximize !== undefined) {
      if (maximize) {
        this.mainWindow.maximize();
      } else {
        this.mainWindow.unmaximize();
      }
      return;
    }

    if (this.mainWindow.isMaximized()) {
      this.mainWindow.unmaximize();
    } else {
      this.mainWindow.maximize();
    }
  }

  // 登录状态管理
  setUserLoggedIn(isLoggedIn: boolean): void {
    this.isUserLoggedIn = isLoggedIn;

    if (isLoggedIn) {
      logger.info('用户已登录，可以显示悬浮窗口');
      // 登录后自动创建悬浮窗口（但不显示）
      if (!this.floatingWindow) {
        this.createFloatingWindow();
      }
    } else {
      logger.info('用户已注销，关闭悬浮窗口');
      // 注销时关闭悬浮窗口
      this.closeFloatingWindow();
    }
  }

  isLoggedIn(): boolean {
    return this.isUserLoggedIn;
  }

  // 向悬浮窗口发送数据
  sendDataToFloatingWindow(channel: string, data?: any): void {
    if (this.floatingWindow && !this.floatingWindow.isDestroyed()) {
      this.floatingWindow.webContents.send(channel, data);
    } else {
      logger.error(`无法向悬浮窗口发送数据，窗口不存在或已销毁: ${channel}, ${data}`);
    }
  }

  // 悬浮窗口向主窗口发送数据
  sendDataToMainWindow(channel: string, data: any): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(channel, data);
    }
  }
}

export default WindowManager;
