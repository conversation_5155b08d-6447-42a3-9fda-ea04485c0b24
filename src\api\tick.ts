import http from './http';
import type { StandardTick, RealtimeTrade } from '@/types';

class TickService {
  /**
   * 获取合约行情数据
   * @param instruments - 合约代码
   */
  static getTick(instruments: string) {
    console.log(1111, instruments);

    return http<StandardTick[]>('/v3/last/tick', {
      method: 'GET',
      params: {
        instruments,
      },
    });
  }

  /**
   * 获取实时成交数据
   * @param instrument - 合约代码
   */
  static getRealtimeTrades(instrument: string) {
    return http<RealtimeTrade[]>('/api/realtime-trades', {
      method: 'GET',
      params: {
        instrument,
      },
    });
  }
}

export default TickService;
