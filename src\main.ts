import './assets/style/main.css';
import 'virtual:uno.css';
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';
import { createApp } from 'vue';
import { createPinia } from 'pinia';

import App from './App.vue';
import router from './router';
import { Misc } from './script';

// 判断如果是在悬浮窗口中则不要调用 Misc.connect
const isFloatingWindow = window.location.hash === '#/floating';
if (!isFloatingWindow) {
  Misc.connect();
}

const app = createApp(App);

app.use(createPinia());
app.use(router);

app.mount('#app');
