import type { TacticStatusEnum, PoolTypeEnum } from '@/enum';

/** 股票池 */
export interface Pool {
  id: number;
  /** 股票池名称 */
  groupName: string;
  /** 状态 */
  status: TacticStatusEnum;
  /** 类型 */
  groupType: PoolTypeEnum;
  /** 个股数量 */
  size: number;
  /** 仓位(0~100%) */
  positionRate: number;
  /** 买入触发配置 */
  strategyConfig: StrategyConfig | null;
  /** 撤单配置 */
  cancelConfig: CancelConfig[] | null;
  /** 运行时间配置 */
  effectTime: EffectTime | null;
  /** 用户ID */
  userId: number;
  /** 用户名 */
  userName: string;
}

/** 股票池推送 */
export type WsPool<T extends 0 | 1> = Pool & { deleted: T };

/** 新建/修改股票池 */
export type FormPool = Pick<Pool, 'groupName' | 'positionRate'>;

/** 运行时间配置 */
export interface EffectTime {
  am: TimeConfig;
  pm: TimeConfig;
}

export interface TimeConfig {
  /** "09:30:00" */
  begin: string;
  /** "11:30:00" */
  end: string;
}

/** 买入触发配置 */
export interface StrategyConfig {
  /** 涨停排名前N名 */
  limitTriggerNum: number | null;
  /** 沪市设置 */
  shConfig: [ExchangeConfig, ExchangeConfig, ExchangeConfig];
  /** 深市设置 */
  szConfig: [ExchangeConfig, ExchangeConfig, ExchangeConfig];
}

/** 交易所设置 */
export interface ExchangeConfig {
  /** 首次排单金额（万） */
  firstAmount: number | null;
  /** 二次排单金额（万） */
  secondAmount: number | null;
  /** 仓位比例(0~100%) */
  positionEffect: number | null;
}

/** 撤单配置 */
export interface CancelConfig1 {
  /** 撤单类型 */
  cancelType: 1;
  /** 撤单信息 */
  cancelInfo: {
    /** 成交时间(ms) */
    tradeTime: number | null;
  };
}

export interface CancelConfig2 {
  /** 撤单类型 */
  cancelType: 2;
  /** 撤单信息 */
  cancelInfo: {
    /** 下单时间(ms) */
    orderTime: number | null;
    /** 前少(万元) */
    frontAmount: number | null;
    /** 后多(万元) */
    afterAmount: number | null;
  };
}

export interface CancelConfig3 {
  /** 撤单类型 */
  cancelType: 3;
  /** 撤单信息 */
  cancelInfo: {
    /** 封单手数 */
    volume: number | null;
    /** 时间(ms) */
    time: number | null;
    /** 回落比例(0~100%) */
    downRate: number | null;
  };
}

export type CancelConfig = CancelConfig1 | CancelConfig2 | CancelConfig3;

/** 股票池详情 */
export interface PoolDetail {
  id: number;
  /** 证券代码 */
  instrument: string;
  /** 证券名称 */
  instrumentName: string;
  /** 状态 */
  status: TacticStatusEnum;
  /** 股票池名称 */
  poolName: string;
  /** 股票池ID */
  poolId: number;
  /** 仓位 */
  positionRate: number;
  /** 策略日志 */
  strategyMsg?: StrategyLog[];
}

/** 股票池详情推送 */
export type WsPoolDetail = PoolDetail & { deleted: 0 | 1 };

export interface TickPoolDetail extends PoolDetail {
  /** 涨跌幅(后端没有该字段，前端从行情数据中获取) */
  risePercent?: number;
}

/** 策略日志 */
export interface StrategyLog {
  /** 触发时间 */
  triggerTime: string;
  /** 触发类型 */
  triggerType: string;
  /** 触发内容 */
  triggerContent: string;
  /** 股票代码 */
  instrument: string;
  /** 股票名称 */
  instrumentName: string;
  /** 买入金额 */
  amount: number;
}
