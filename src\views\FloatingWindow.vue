<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, shallowRef } from 'vue';
import type { InstrumentInfo, Pool, PoolDetail } from '@/types';
import { isStopped } from '@/enum';
import { createLogger } from '@/libs/logger';
import PoolService from '@/api/pool';
import { useInstrumentStore } from '@/stores';

const { getStockInstruments } = useInstrumentStore();

const allInstruments = shallowRef<InstrumentInfo[]>([]);

const logger = createLogger('FloatingWindow');

// 响应式数据
const currentInstrument = ref<InstrumentInfo | null>(null);
const allPools = ref<Pool[]>([]);
const allPoolDetails = ref<PoolDetail[]>([]);

const currentStockDetail = computed(() => {
  if (!currentInstrument.value) return null;
  return (
    allPoolDetails.value.find(
      detail => detail.instrument === currentInstrument.value?.instrument1,
    ) || null
  );
});
const canStart = computed(() => currentStockDetail.value && isStopped(currentStockDetail.value));
const canStop = computed(() => currentStockDetail.value && !isStopped(currentStockDetail.value));

// 获取股票池数据
const fetchPoolsData = async () => {
  try {
    const { errorCode, data } = await PoolService.getStrategyPools();
    if (errorCode === 0 && Array.isArray(data)) {
      allPools.value = data;
      logger.info('获取股票池数据成功', { poolCount: data.length });
    } else {
      logger.error('获取股票池数据失败', { errorCode });
    }
  } catch (error) {
    logger.error('获取股票池数据异常', error);
  }
};

// 获取股票池详情数据
const fetchPoolDetailsData = async () => {
  try {
    const { errorCode, data } = await PoolService.getAllPoolDetails();
    if (errorCode === 0 && Array.isArray(data)) {
      allPoolDetails.value = data;
      logger.info('获取股票池详情数据成功', { detailCount: data.length });
    } else {
      logger.error('获取股票池详情数据失败', { errorCode });
    }
  } catch (error) {
    logger.error('获取股票池详情数据异常', error);
  }
};

// 获取所有数据
const fetchAllData = async () => {
  await Promise.all([fetchPoolsData(), fetchPoolDetailsData()]);
};

// 启动股票自动买入
const startStockAutoBuy = async () => {
  if (!currentStockDetail.value) return;
  try {
    const { errorCode } = await PoolService.updatePoolDetailStatus(
      currentStockDetail.value.id,
      true,
    );
    if (errorCode === 0) {
      logger.info('启动股票自动买入成功', { stockCode: currentInstrument.value?.instrument1 });
      // 刷新数据
      await fetchPoolDetailsData();
    } else {
      logger.error('启动股票自动买入失败', { errorCode });
    }
  } catch (error) {
    logger.error('启动股票自动买入异常', error);
  }
};

// 停止股票自动买入
const stopStockAutoBuy = async () => {
  if (!currentStockDetail.value) return;
  try {
    const { errorCode } = await PoolService.updatePoolDetailStatus(
      currentStockDetail.value.id,
      false,
    );
    if (errorCode === 0) {
      logger.info('停止股票自动买入成功', { stockCode: currentInstrument.value?.instrument1 });
      // 刷新数据
      await fetchPoolDetailsData();
    } else {
      logger.error('停止股票自动买入失败', { errorCode });
    }
  } catch (error) {
    logger.error('停止股票自动买入异常', error);
  }
};

// 将股票加入股票池
const addStockToPool = async (pool: Pool) => {
  if (!currentInstrument.value) return;
  try {
    const { errorCode } = await PoolService.addPoolDetail({
      poolId: pool.id,
      instrument: currentInstrument.value.instrument1,
      instrumentName: currentInstrument.value.instrumentName,
      poolName: pool.groupName,
      positionRate: pool.positionRate,
    });
    if (errorCode === 0) {
      logger.info('将股票加入股票池成功', {
        stockCode: currentInstrument.value.instrument1,
        poolName: pool.groupName,
      });
      // 刷新数据
      await fetchPoolDetailsData();
    } else {
      logger.error('将股票加入股票池失败', { errorCode });
    }
  } catch (error) {
    logger.error('将股票加入股票池异常', error);
  }
};

// 处理股票代码捕获
const handleStockCodeCaptured = (_: Electron.IpcRendererEvent, stockCode: string) => {
  logger.info('捕获到股票代码', { stockCode });
  const matched = allInstruments.value.find(instrument => instrument.instrument === stockCode);
  if (matched) {
    currentInstrument.value = matched;
  } else {
    logger.warn('捕获到的股票代码不存在', { stockCode });
    currentInstrument.value = null;
  }
};

onMounted(async () => {
  // 监听股票代码捕获事件
  window.ipcRenderer.on('stock-code-captured', handleStockCodeCaptured);
  // 获取初始数据
  fetchAllData();
  // 监听数据更新事件
  window.ipcRenderer.on('pools-data-updated', fetchPoolsData);
  window.ipcRenderer.on('pool-details-data-updated', fetchPoolDetailsData);
  // 获取全量股票
  allInstruments.value = await getStockInstruments();
});

onUnmounted(() => {
  // 取消监听事件
  window.ipcRenderer.off('stock-code-captured', handleStockCodeCaptured);
  window.ipcRenderer.off('pools-data-updated', fetchPoolsData);
  window.ipcRenderer.off('pool-details-data-updated', fetchPoolDetailsData);
});
</script>

<template>
  <div class="floating-window" flex aic gap-10 px-20>
    <div class="drag" cursor-move>
      <i fs-20 block i-mdi-drag-horizontal></i>
    </div>
    <div class="no-drag" w-130 flex aic jcc>
      {{
        currentInstrument
          ? currentInstrument.instrumentName + ' ' + currentInstrument.instrument1
          : '--'
      }}
    </div>
    <el-button
      @click="startStockAutoBuy"
      :disabled="!canStart"
      :class="{ 'cursor-not-allowed': !canStart }"
      class="no-drag"
      color="var(--g-bg-green-l)"
    >
      <i mr-4 fs-14 i-mdi-motion-play-outline />
      启动
    </el-button>
    <el-button
      @click="stopStockAutoBuy"
      :disabled="!canStop"
      :class="{ 'cursor-not-allowed': !canStop }"
      class="no-drag"
      color="var(--g-red-l)"
    >
      <i mr-4 fs-14 i-mdi-motion-pause-outline />
      停止
    </el-button>
    <el-button
      :color="currentStockDetail?.poolId === pool.id ? 'var(--g-primary-l2)' : 'var(--g-panel-bg)'"
      v-for="pool in allPools"
      :key="pool.id"
      :disabled="!currentInstrument || !!currentStockDetail"
      class="no-drag"
      @click="addStockToPool(pool)"
    >
      {{ pool.groupName }}
    </el-button>
  </div>
</template>

<style scoped>
.floating-window {
  .drag {
    -webkit-app-region: drag;
  }
  .no-drag {
    -webkit-app-region: no-drag;
  }
}
</style>
