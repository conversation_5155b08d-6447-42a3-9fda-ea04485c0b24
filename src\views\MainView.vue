<script setup lang="ts">
import TopBar from '@/components/MainView/TopBar.vue';
import MainContent from '@/components/MainView/MainContent.vue';
import MainTabs from '@/components/MainView/MainTabs.vue';
import { useTemplateRef } from 'vue';
import { useWebSocketPush } from '@/composables/useWebSocketPush';

const mainTabsRef = useTemplateRef('mainTabsRef');

// 通知悬浮窗口数据有更新
const { onPoolUpdate, onPoolDetailUpdate } = useWebSocketPush();

onPoolUpdate(() => {
  window.ipcRenderer.send('pools-data-updated');
});

onPoolDetailUpdate(() => {
  window.ipcRenderer.send('pool-details-data-updated');
});
</script>

<template>
  <div h-full flex="~ col">
    <TopBar />
    <MainTabs ref="mainTabsRef" />
    <MainContent flex-1 min-h-1 :active-tab="mainTabsRef?.activeName" />
  </div>
</template>

<style scoped></style>
